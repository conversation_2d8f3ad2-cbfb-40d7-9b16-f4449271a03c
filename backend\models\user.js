import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  firebaseUid: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ["user", "company", "admin"], default: "user" },
  balance: { type: Number, default: 0 },
  creditInfo: {
    type: {
      credit_number: { type: Number, maxlength: 16, minlength: 16 },
    },
  },
  img: { type: String, default: "" },
});

export const User = mongoose.model("User", userSchema);
