const faq: { question: string; answer: string }[] = [
  {
    question: "What is this app for?",
    answer:
      "This app helps users classify waste materials using AI-powered image recognition. It identifies waste types such as plastic, metal, glass, paper, and organic waste, making recycling easier.",
  },
  {
    question: "How do I use the waste classification feature?",
    answer:
      "Simply take a picture of the waste item using the camera or upload an image from your gallery. The app will analyze the image and tell you the waste category.",
  },
  {
    question: "Is the classification 100% accurate?",
    answer:
      "While our AI model is highly trained, there may be occasional errors. If you believe a classification is incorrect, you can manually adjust it and provide feedback to improve accuracy.",
  },
  {
    question: "Can I sell or buy waste items through this app?",
    answer:
      "Yes! The app includes a Marketplace where users can sell recyclable waste to buyers or find materials they need.",
  },
  {
    question: "How can I contribute to better waste management?",
    answer:
      "You can help by properly sorting your waste, using this app for guidance, and spreading awareness about responsible recycling.",
  },
  {
    question: "Does the app work offline?",
    answer:
      "Some features, such as waste classification, require an internet connection. However, you can access general information and previously classified waste offline.",
  },
  {
    question: "Is my data stored or shared?",
    answer:
      "We respect your privacy. Images are only processed for classification and are not stored unless you choose to save them.",
  },
  {
    question: "Who can I contact for support?",
    answer:
      "If you have any issues, you can reach out through the Help & Support section in the app or email us at [<EMAIL>].",
  },
];

export default faq;
