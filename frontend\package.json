{"name": "trash2cash_gp", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tamagui/colors": "^1.124.18", "@tamagui/config": "^1.124.18", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "~52.0.33", "expo-blur": "~14.0.3", "expo-constants": "~17.0.5", "expo-file-system": "^18.0.11", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image-picker": "^16.0.6", "expo-linking": "~7.0.5", "expo-location": "^18.0.7", "expo-router": "~4.0.17", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.21", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.1", "lucide": "^0.475.0", "lucide-react": "^0.475.0", "lucide-react-native": "^0.475.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hot-toast": "^2.5.2", "react-native": "0.76.7", "react-native-app-intro-slider": "^4.0.4", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.10", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "^1.20.1", "react-native-onboarding-swiper": "^1.3.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tamagui": "^1.124.18", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.8.2"}, "private": true, "expo": {"plugins": [["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow trash2cash to access your Face ID biometric data."}]]}}