{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "expo-secure-store": "^14.0.1", "express": "^4.21.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.10.1"}, "devDependencies": {"nodemon": "^3.1.9"}}