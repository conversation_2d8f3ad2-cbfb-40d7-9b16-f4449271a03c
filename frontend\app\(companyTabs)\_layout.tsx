import { Tabs, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { Platform, StyleSheet, Text, View } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "@/store/context";
import {
  ChartPie,
  Package,
  Plus,
  Store,
  User,
  Wallet,
} from "lucide-react-native";
export default function CompanyTabLayout() {
  console.log("Company");
  const { authUser } = useAuth();
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  useEffect(() => {
    if (authUser === undefined) return;
    console.log("company layout authUser", authUser);

    const timer = setTimeout(() => {
      setIsReady(true);
      
      if (authUser?.role === "admin") {
        router.replace("/(adminTabs)");
      } else if (authUser?.role === "user") {
        router.replace("/(tabs)");
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [authUser]);

  if (!isReady) {
    return null;
  }
  return (
    <View style={{ flex: 1 }}>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors.black50opacity,
          tabBarInactiveTintColor: "black",
          headerShown: false,
          tabBarButton: HapticTab,
          tabBarBackground: TabBarBackground,
          tabBarStyle: Platform.select({
            ios: {
              position: "absolute",
            },
            default: {},
          }),
        }}
      >
        {/* <Tabs.Screen
          name="orders"
          options={{
            title: "Orders",
            tabBarIcon: ({ color }) => <Package color={color} size={25} />,
          }}
        /> */}

        <Tabs.Screen
          name="company_store"
          options={{
            title: "Store",
            tabBarIcon: ({ color }) => <Store size={24} color={color} />,
          }}
        />
        <Tabs.Screen
          name="wallet"
          options={{
            title: "Wallet",
            tabBarIcon: ({ color }) => <Wallet size={25} color={color} />,
          }}
        />
        <Tabs.Screen
          name="index"
          options={{
            title: "Add",
            tabBarIcon: ({ color }) => <Plus size={25} color={color} />,
          }}
        />
        {/* <Tabs.Screen
          name="shop"
          options={{
            title: "Shop",
            tabBarIcon: ({ color }) => <Store size={25} color={color} />,
          }}
        /> */}
        {/* <Tabs.Screen
          name="dashboard"
          options={{
            title: "Dashboard",
            tabBarIcon: ({ color }) => <ChartPie size={24} color={color} />,
          }}
        /> */}

        <Tabs.Screen
          name="bin"
          options={{
            title: "Bin",
            tabBarIcon: ({ color }) => (
              <Ionicons size={25} color={color} name="trash-bin" />
            ),
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: "Profile",
            tabBarIcon: ({ color }) => <User size={25} color={color} />,
          }}
        />
      </Tabs>
    </View>
  );
}
