import Logo from "@/components/Logo";
import { Colors } from "@/constants/Colors";
import { useDash } from "@/store/dashBoard";
import { useLocalSearchParams } from "expo-router";
import { Edit3, X } from "lucide-react-native";
import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { View } from "tamagui";

export default function UsersScreen() {
  const { type } = useLocalSearchParams();
  const { users, companies, deleteUser } = useDash();

  const handleDeleteUser = async (id: any) => {
    await deleteUser(id);
  };

  return (
    <View style={styles.container}>
      <Logo />
      {type == "User"
        ? users.map((user: any) => {
            return (
              <View key={user._id} style={[styles.listItem, styles.shadowBox]}>
                <Text style={styles.title}>{user.name}</Text>
                <View style={styles.secondContainer}>
                  <View style={styles.badge}>
                    <Text style={styles.title}>{user.role}</Text>
                  </View>
                  <View style={styles.actionsContainer}>
                    <TouchableOpacity onPress={() => console.log("edit")}>
                      <Edit3 color={"black"} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleDeleteUser(user._id)}
                    >
                      <X color={"red"} />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            );
          })
        : companies.map((company: any) => {
            return (
              <View
                key={company._id}
                style={[styles.listItem, styles.shadowBox]}
              >
                <Text style={styles.title}>{company.name}</Text>
                <View style={styles.secondContainer}>
                  <View style={styles.badge}>
                    <Text style={styles.title}>{company.role}</Text>
                  </View>
                  <View style={styles.actionsContainer}>
                    <TouchableOpacity
                      onPress={() => handleDeleteUser(company._id)}
                    >
                      <X color={"red"} />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            );
          })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: "white",
  },
  header: {
    fontSize: 32,
    color: "#2B4B40",
    textAlign: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 17,
    color: Colors.header,
  },
  listItem: {
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginVertical: 4,
  },
  shadowBox: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,

    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,

    elevation: 5,
  },
  badge: {
    backgroundColor: "#D9D9D9",
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  secondContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  actionsContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
});
