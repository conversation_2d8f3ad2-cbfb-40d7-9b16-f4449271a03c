# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Parameters
nc: 80 # number of classes
depth_multiple: 1.0 # model depth multiple
width_multiple: 1.0 # layer channel multiple
anchors: 3 # AutoAnchor evolves 3 anchors per P output layer

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [
    [-1, 1, Conv, [64, 6, 2, 2]], # 0-P1/2
    [-1, 1, Conv, [128, 3, 2]], # 1-P2/4
    [-1, 3, C3, [128]],
    [-1, 1, Conv, [256, 3, 2]], # 3-P3/8
    [-1, 6, C3, [256]],
    [-1, 1, Conv, [512, 3, 2]], # 5-P4/16
    [-1, 9, C3, [512]],
    [-1, 1, Conv, [768, 3, 2]], # 7-P5/32
    [-1, 3, C3, [768]],
    [-1, 1, Conv, [1024, 3, 2]], # 9-P6/64
    [-1, 3, C3, [1024]],
    [-1, 1, <PERSON>P<PERSON>, [1024, 5]], # 11
  ]

# YOLOv5 v6.0 head with (P3, P4, P5, P6) outputs
head: [
    [-1, 1, Conv, [768, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 8], 1, Concat, [1]], # cat backbone P5
    [-1, 3, C3, [768, False]], # 15

    [-1, 1, Conv, [512, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 6], 1, Concat, [1]], # cat backbone P4
    [-1, 3, C3, [512, False]], # 19

    [-1, 1, Conv, [256, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 4], 1, Concat, [1]], # cat backbone P3
    [-1, 3, C3, [256, False]], # 23 (P3/8-small)

    [-1, 1, Conv, [256, 3, 2]],
    [[-1, 20], 1, Concat, [1]], # cat head P4
    [-1, 3, C3, [512, False]], # 26 (P4/16-medium)

    [-1, 1, Conv, [512, 3, 2]],
    [[-1, 16], 1, Concat, [1]], # cat head P5
    [-1, 3, C3, [768, False]], # 29 (P5/32-large)

    [-1, 1, Conv, [768, 3, 2]],
    [[-1, 12], 1, Concat, [1]], # cat head P6
    [-1, 3, C3, [1024, False]], # 32 (P6/64-xlarge)

    [[23, 26, 29, 32], 1, Detect, [nc, anchors]], # Detect(P3, P4, P5, P6)
  ]
