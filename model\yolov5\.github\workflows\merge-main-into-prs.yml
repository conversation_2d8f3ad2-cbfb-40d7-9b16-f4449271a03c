# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Automatically merges repository 'main' branch into all open PRs to keep them up-to-date
# Action runs on updates to main branch so when one PR merges to main all others update

name: Merge main into PRs

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - ${{ github.event.repository.default_branch }}

jobs:
  Merge:
    if: github.repository == 'ultralytics/yolov5'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-python@v5
        with:
          python-version: "3.x"
          cache: "pip"
      - name: Install requirements
        run: |
          pip install pygithub
      - name: Merge default branch into PRs
        shell: python
        run: |
          from github import Github
          import os

          g = Github(os.getenv('GITHUB_TOKEN'))
          repo = g.get_repo(os.getenv('GITHUB_REPOSITORY'))

          # Fetch the default branch name
          default_branch_name = repo.default_branch
          default_branch = repo.get_branch(default_branch_name)

          for pr in repo.get_pulls(state='open', sort='created'):
              try:
                  # Get full names for repositories and branches
                  base_repo_name = repo.full_name
                  head_repo_name = pr.head.repo.full_name
                  base_branch_name = pr.base.ref
                  head_branch_name = pr.head.ref

                  # Check if PR is behind the default branch
                  comparison = repo.compare(default_branch.commit.sha, pr.head.sha)
                  
                  if comparison.behind_by > 0:
                      print(f"⚠️ PR #{pr.number} ({head_repo_name}:{head_branch_name} -> {base_repo_name}:{base_branch_name}) is behind {default_branch_name} by {comparison.behind_by} commit(s).")
                      
                      # Attempt to update the branch
                      try:
                          success = pr.update_branch()
                          assert success, "Branch update failed"
                          print(f"✅ Successfully merged '{default_branch_name}' into PR #{pr.number} ({head_repo_name}:{head_branch_name} -> {base_repo_name}:{base_branch_name}).")
                      except Exception as update_error:
                          print(f"❌ Could not update PR #{pr.number} ({head_repo_name}:{head_branch_name} -> {base_repo_name}:{base_branch_name}): {update_error}")
                          print("   This might be due to branch protection rules or insufficient permissions.")
                  else:
                      print(f"✅ PR #{pr.number} ({head_repo_name}:{head_branch_name} -> {base_repo_name}:{base_branch_name}) is up to date with {default_branch_name}.")
              except Exception as e:
                  print(f"❌ Could not process PR #{pr.number}: {e}")

        env:
          GITHUB_TOKEN: ${{ secrets._GITHUB_TOKEN }}
          GITHUB_REPOSITORY: ${{ github.repository }}
