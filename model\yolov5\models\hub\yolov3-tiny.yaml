# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Parameters
nc: 80 # number of classes
depth_multiple: 1.0 # model depth multiple
width_multiple: 1.0 # layer channel multiple
anchors:
  - [10, 14, 23, 27, 37, 58] # P4/16
  - [81, 82, 135, 169, 344, 319] # P5/32

# YOLOv3-tiny backbone
backbone:
  # [from, number, module, args]
  [
    [-1, 1, Conv, [16, 3, 1]], # 0
    [-1, 1, nn.<PERSON><PERSON>ool2d, [2, 2, 0]], # 1-P1/2
    [-1, 1, Conv, [32, 3, 1]],
    [-1, 1, nn.<PERSON><PERSON>ool2d, [2, 2, 0]], # 3-P2/4
    [-1, 1, Conv, [64, 3, 1]],
    [-1, 1, nn.<PERSON><PERSON>ool2d, [2, 2, 0]], # 5-P3/8
    [-1, 1, Conv, [128, 3, 1]],
    [-1, 1, nn.<PERSON><PERSON><PERSON>2<PERSON>, [2, 2, 0]], # 7-P4/16
    [-1, 1, Conv, [256, 3, 1]],
    [-1, 1, nn.<PERSON><PERSON>ool2d, [2, 2, 0]], # 9-P5/32
    [-1, 1, Conv, [512, 3, 1]],
    [-1, 1, nn.ZeroPad2d, [[0, 1, 0, 1]]], # 11
    [-1, 1, nn.MaxPool2d, [2, 1, 0]], # 12
  ]

# YOLOv3-tiny head
head: [
    [-1, 1, Conv, [1024, 3, 1]],
    [-1, 1, Conv, [256, 1, 1]],
    [-1, 1, Conv, [512, 3, 1]], # 15 (P5/32-large)

    [-2, 1, Conv, [128, 1, 1]],
    [-1, 1, nn.Upsample, [None, 2, "nearest"]],
    [[-1, 8], 1, Concat, [1]], # cat backbone P4
    [-1, 1, Conv, [256, 3, 1]], # 19 (P4/16-medium)

    [[19, 15], 1, Detect, [nc, anchors]], # Detect(P4, P5)
  ]
