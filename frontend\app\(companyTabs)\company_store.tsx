import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { FontAwesome5 } from "@expo/vector-icons";
import { Button } from "tamagui";
import {
  Plus,
  Minus,
  Milk,
  Anvil,
  Wine,
  Book,
  Package,
  ShoppingCart,
} from "lucide-react-native";
import Header from "@/components/Header";
import { useInventory } from "@/store/InventoryContext";
import { useCart } from "@/store/cartContext";
import Toast from "react-native-toast-message";

export default function CompanyStore() {
  const { companyShop, getCompanyShop } = useInventory();
  const { addToCart } = useCart();

  useEffect(() => {
    getCompanyShop();
  }, [companyShop, getCompanyShop]);

  useEffect(() => {
    const updatedIcons =
      companyShop?.items.map((item) => {
        switch (item.name) {
          case "Metal":
            return <Anvil color="black" />;
          case "Glass":
            return <Wine color="black" />;
          case "Paper":
            return <Book color="black" />;
          case "Plastic":
            return <Milk color="black" />;
          case "Cardboard":
            return <Package color="black" />;
          default:
            return <Milk color="black" />;
        }
      }) || [];
    setIcons(updatedIcons);
  }, [companyShop]);

  const [quantities, setQuantities] = useState<number[]>(
    companyShop?.items.map((item) => Math.min(item.quantity, 100)) || [0]
  );

  const [icons, setIcons] = useState<JSX.Element[]>(
    companyShop?.items.map(() => <Milk color="black" />) || []
  );

  const [cart, setCart] = useState<boolean[]>(
    companyShop?.items.map(() => false) || []
  );
  const [cartCount, setCartCount] = useState(0);

  const increaseWeight = (itemIndex: number) => {
    setQuantities((prevQuantities) => {
      const updatedQuantities = [...prevQuantities];
      updatedQuantities[itemIndex] += 5;
      return updatedQuantities;
    });
  };

  const decreaseWeight = (itemIndex: number) => {
    setQuantities((prevQuantities) => {
      const updatedQuantities = [...prevQuantities];
      if (updatedQuantities[itemIndex] >= 5) {
        updatedQuantities[itemIndex] -= 5;
      }
      return updatedQuantities;
    });
  };

  const toggleCart = (itemIndex: number) => {
    setCart((prevCart) => {
      const updatedCart = [...prevCart];
      const currentItemInCart = updatedCart[itemIndex];

      if (currentItemInCart) {
        setCartCount((prevCount) => Math.max(prevCount - 1, 0));
      } else {
        setCartCount((prevCount) => prevCount + 1);
      }

      updatedCart[itemIndex] = !updatedCart[itemIndex];
      return updatedCart;
    });
  };

  const handleAddToCart = (item: any) => {
    addToCart(item);
    Toast.show({
      type: "success",
      text1: `${item.quantity} items of ${item.wasteType} added to cart`,
    });
  }

  return (
    <>
      <ScrollView style={styles.container}>
        <Header />

        <View style={styles.cartContainer}>
          <ShoppingCart color="black" size={30} />
          {cartCount > 0 && (
            <View style={styles.cartBadge}>
              <Text style={styles.cartBadgeText}>{cartCount}</Text>
            </View>
          )}
        </View>

        <View style={styles.companyContainer}>
          {companyShop?.items.map((item, itemIndex) => (
            <View style={[styles.card, styles.shadowBox]} key={item.name}>
              <View style={styles.itemRow}>
                {icons[itemIndex]}
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.price}>{item.price} EGP / Item</Text>
                <Text style={styles.weight}>{item.quantity} Items</Text>
              </View>

              <View style={styles.quantityContainer}>
                <Button
                  circular
                  size={"$2"}
                  icon={Plus}
                  borderColor="$color"
                  borderWidth={1.5}
                  backgroundColor="transparent"
                  pressStyle={{ opacity: 0.5 }}
                  color={"black"}
                  onPress={() => increaseWeight(itemIndex)}
                />

                <View>
                  <Text>{quantities[itemIndex]} Item</Text>
                </View>

                <Button
                  circular
                  size={"$2"}
                  icon={Minus}
                  borderColor="$color"
                  borderWidth={1.5}
                  backgroundColor="transparent"
                  pressStyle={{ opacity: 0.5 }}
                  color={"black"}
                  onPress={() => decreaseWeight(itemIndex)}
                />

                <TouchableOpacity
                  style={[
                    styles.addButton,
                  ]}
                  // onPress={() => toggleCart(itemIndex)}
                  onPress={() => handleAddToCart({_id: itemIndex.toString(), wasteType: item.name, quantity: quantities[itemIndex], price: item.price, sellerId: "67c42cdf06be297aa9b28bd8"})}
                >
                  <Text style={styles.addButtonText}>
                    {cart[itemIndex] ? "Cancel" : "Add to cart"}
                  </Text>
                  <FontAwesome5
                    name={"shopping-cart"}
                    size={16}
                    color="white"
                  />
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 0,
    padding: 16,
    backgroundColor: "#FDFDFD",
  },
  header: {
    fontSize: 40,
    color: "#2B4B40",
    textAlign: "center",
    marginBottom: 20,
    marginTop: 20,
  },
  companyContainer: {
    marginBottom: "4%",
  },
  card: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 16,

    marginBottom: 10,
  },
  shadowBox: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 4,
  },
  itemRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 30,
  },
  itemName: {
    fontSize: 18,
    fontWeight: "500",
  },
  price: {
    backgroundColor: "#2B4B40",
    color: "white",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 5,
    fontSize: 14,
  },
  weight: {
    fontSize: 16,
    borderWidth: 2,
    borderColor: "#E0E0E0",
    padding: 5,
    borderRadius: 7,
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginVertical: 5,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#18AE7B",
    paddingVertical: 12,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginLeft: 20,
  },
  cancelButton: {
    backgroundColor: "#E74C3C",
  },
  addButtonText: {
    color: "white",
    fontWeight: "500",
    marginRight: 8,
  },
  cartContainer: {
    position: "absolute",
    top: 60,
    right: 80,
    zIndex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  cartBadge: {
    position: "absolute",
    top: -15,
    right: 0,
    backgroundColor: "#E74C3C",
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  cartBadgeText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 12,
  },
});
